/*
 * Copyright (c) 2012-2014. Sencha Inc.
 */

package com.sencha.tools.compiler.ast.js;

/**
 * Represents optional chaining function calls in JavaScript ES2020.
 * Handles syntax like: obj?.method() or obj?.method?.()
 * 
 * @since ES2020 (ES11)
 */
public class OptionalFunctionCall extends FunctionCall {
    
    private boolean _startOfOptionalChain = false;
    private boolean _hasTrailingComma = false;
    
    /**
     * Default constructor.
     */
    public OptionalFunctionCall() {
        super();
    }
    
    @Override
    <T> void doVisit(NodeVisitor<T> vis) {
        vis.onOptionalFunctionCall(this);
    }
    
    /**
     * Gets whether this call is the start of an optional chaining sequence.
     * 
     * @return true if this is the start of optional chaining, false otherwise
     */
    public boolean isStartOfOptionalChain() {
        return _startOfOptionalChain;
    }
    
    /**
     * Sets whether this call is the start of an optional chaining sequence.
     * 
     * @param startOfOptionalChain true if this is the start of optional chaining
     */
    public void setStartOfOptionalChain(boolean startOfOptionalChain) {
        this._startOfOptionalChain = startOfOptionalChain;
    }
    
    /**
     * Gets whether this call has a trailing comma in arguments.
     * 
     * @return true if there's a trailing comma, false otherwise
     */
    public boolean hasTrailingComma() {
        return _hasTrailingComma;
    }
    
    /**
     * Sets whether this call has a trailing comma in arguments.
     * 
     * @param hasTrailingComma true if there's a trailing comma
     */
    public void setTrailingComma(boolean hasTrailingComma) {
        this._hasTrailingComma = hasTrailingComma;
    }
}
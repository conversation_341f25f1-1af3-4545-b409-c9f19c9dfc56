/*
 * Copyright (c) 2012-2014. Sencha Inc.
 */

package com.sencha.tools.compiler.ast.js;

public interface NodeVisitor<T> {
    void setContext(T context);
    void visit(BaseNode node);

    T getContext();
    void onObjectLiteral(ObjectLiteral node);
    void onObjectProperty(ObjectProperty node);
    void onStringLiteral(StringLiteral node);
    void onBlock(Block node);
    void onLineComment(LineComment node);
    void onBlockComment(BlockComment node);
    void onScope(Scope node);
    void onArrayLiteral(ArrayLiteral node);
    void onConditionalExpression(ConditionalExpression node);
    void onAssignment(Assignment node);
    void onInfix(Infix node);
    void onElementGet(ElementGet node);
    void onUnary(Unary node);
    void onEmptyExpression(EmptyExpression node);
    void onError(ErrorNode node);
    void onExpressionStatement(ExpressionStatement node);
    void onFunctionCall(FunctionCall node);
    void onIfStatement(IfStatement node);
    void onJumpNode(JumpNode node);
    void onKeywordLiteral(KeywordLiteral node);
    void onLabel(Label node);
    void onLabeledStatement(LabeledStatement node);
    void onName(Name node);
    void onNumberLiteral(NumberLiteral node);
    void onParenthesizedExpression(ParenthesizedExpression node);
    void onRegExpLiteral(RegExpLiteral node);
    void onReturnStatement(ReturnStatement node);
    void onSwitchCase(SwitchCase node);
    void onThrowStatement(ThrowStatement node);
    void onTryStatement(TryStatement node);
    void onVariableDeclaration(VariableDeclaration node);
    void onVariableInitializer(VariableInitializer node);
    void onWithStatement(WithStatement node);
    void onXmlFragment(XmlFragment node);
    void onXmlLiteral(XmlLiteral node);
    void onXmlRef(XmlRef node);
    void onYieldStatement(YieldStatement node);
    void onScriptNode(ScriptNode node);
    void onFunctionNode(FunctionNode node);
    void onOptionalFunctionCall(OptionalFunctionCall node);
    void onBreakStatement(BreakStatement node);
    void onContinueStatement(ContinueStatement node);
    void onSwitchStatement(SwitchStatement node);
    void onForInLoop(ForInLoop node);
    void onArrayComprehensionLoop(ArrayComprehensionLoop node);
    void onArrayComprehension(ArrayComprehension node);
    void onLoop(Loop node);
    void onLetNode(LetNode node);
    void onDoLoop(DoLoop node);
    void onForLoop(ForLoop node);
    void onWhileLoop(WhileLoop node);
    void onNewExpression(NewExpression node);
    void onRootNode(RootNode node);
    void onPropertyGet(PropertyGet node);
    void onCatchClause(CatchClause node);

    void onClassDeclaration(ClassDeclaration node);
    void onGetAccessor(GetAccessor node);
    void onSetAccessor(SetAccessor node);
    void onDefaultParameter(DefaultParameter node);
    void onSpreadExpression(SpreadExpression node);
    void onObjectSpread(ObjectSpread node);
    void onOptionalMemberExpression(OptionalMemberExpression node);
    void onRestParameter(RestParameter node);
    void onImportSpecifier(ImportSpecifier node);
    void onImportDeclaration(ImportDeclaration node);
    void onExportDeclaration(ExportDeclaration node);
    void onExportSpecifier(ExportSpecifier node);
    void onForOfLoop(ForOfLoop node);
    void onAwaitExpression(AwaitExpression node);
    void onTemplateLiteralExpression(TemplateLiteralExpression node);
    void onTemplateLiteralPortion(TemplateLiteralPortion node);
    void onTemplateSubstitution(TemplateSubstitution node);
    void onComputedName(ComputedName node);
    void onArrayPattern(ArrayPattern node);
    void onObjectPattern(ObjectPattern node);

    void onFormalParameterList(FormalParameterList node);
    void onOptionalMemberLookUpExpression(OptionalMemberLookUpExpression node);
    void onForAwaitOfStatement(ForAwaitOfStatement node);
    void onPassthroughNode(PassthroughNode node);
    void onSimplePassthroughNode(SimplePassthroughNode node);

}

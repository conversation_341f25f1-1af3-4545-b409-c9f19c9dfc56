/*
 * Copyright (c) 2012-2014. Sencha Inc.
 */

package com.sencha.tools.compiler.ast.js;


import org.mozilla.javascript.Token;

import java.util.HashMap;
import java.util.Map;

public class KeywordLiteral extends ValueLiteral {
    private static Map<Integer, String> _values = new HashMap<Integer, String>(){{
        put(Token.THIS, "this");
        put(Token.NULL, "null");
        put(Token.TRUE, "true");
        put(Token.FALSE, "false");
        put(Token.DEBUGGER, "debugger");
        put(Token.DEFAULT, "default");
    }};

    private int _code;

    @Override
    <T> void doVisit(NodeVisitor<T> vis) {
        vis.onKeywordLiteral(this);
    }

    public int getCode() {
        return _code;
    }

    public void setCode(int code) {
        _code = code;
    }

    @Override
    public String getValue() {
        return _values.get(_code);
    }

    public boolean isBooleanLiteral() {
        return _code == Token.TRUE || _code == Token.FALSE;
    }
}

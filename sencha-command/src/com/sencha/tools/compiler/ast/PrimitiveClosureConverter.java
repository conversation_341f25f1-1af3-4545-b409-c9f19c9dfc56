/*
 * Copyright (c) 2012-2024. Sencha Inc.
 */

package com.sencha.tools.compiler.ast;

import com.google.javascript.jscomp.parsing.parser.trees.*;
import com.sencha.tools.compiler.ast.js.*;

/**
 * PrimitiveClosureConverter - A simplified converter that focuses purely on parsing
 * Google Closure ParseTree nodes into Sencha's custom AST nodes WITHOUT any transpilation.
 * 
 * Unlike BasicClosureConverter, this converter:
 * - Does NOT handle ES5 transpilation
 * - Does NOT use PassthroughNode for modern syntax
 * - Does NOT have _preserveModernSyntax flag
 * - Simply converts ParseTree → BaseNode directly
 * 
 * This is ideal when you want to preserve all modern JavaScript syntax in the AST
 * and handle any transpilation separately (or not at all).
 */
public class PrimitiveClosureConverter extends ClosureConverter implements ClosureASTConverter {
    
    private String _sourceName;
    private String _originalSource;
    
    public PrimitiveClosureConverter() {
        super();
    }
    
    public void setSourceName(String sourceName) {
        this._sourceName = sourceName;
    }
    
    public void setOriginalSource(String originalSource) {
        this._originalSource = originalSource;
    }

    /**
     * Main conversion method that delegates to specific convert methods
     */
    @Override
    public BaseNode doConvert(ParseTree tree, BaseNode parent) {
        if (tree == null) {
            return null;
        }

        // Use the inherited converter map from ClosureConverter
        ClosureNodeConverter converter = getConverter(tree);
        if (converter != null) {
            return converter.convert(tree, this);
        }

        // Fallback - should not happen if all converters are properly registered
        throw new RuntimeException("No converter found for: " + tree.getClass().getSimpleName());
    }

    // ========== CORE CONVERSION METHODS ==========
    
    @Override
    public BaseNode convert(ProgramTree tree) {
        RootNode root = new RootNode();
        root.setSourceFile(createSourceFile());
        
        for (ParseTree statement : tree.sourceElements) {
            BaseNode node = doConvert(statement, root);
            if (node != null) {
                root.addElement(node);
            }
        }
        
        return root;
    }
    
    @Override
    public BaseNode convert(BlockTree tree) {
        Block block = new Block();
        block.setSourceFile(createSourceFile());
        
        for (ParseTree statement : tree.statements) {
            BaseNode node = doConvert(statement, block);
            if (node != null) {
                block.addElement(node);
            }
        }
        
        return block;
    }
    
    @Override
    public BaseNode convert(FunctionDeclarationTree tree) {
        FunctionNode func = new FunctionNode();
        func.setSourceFile(createSourceFile());
        
        // Set function name
        if (tree.name != null) {
            Name name = new Name(tree.name.value);
            func.setName(name);
        }
        
        // Add parameters
        for (ParseTree param : tree.formalParameterList.parameters) {
            BaseNode paramNode = doConvert(param, func);
            if (paramNode instanceof Name) {
                func.addParam((Name) paramNode);
            }
        }
        
        // Set function body
        BaseNode body = doConvert(tree.functionBody, func);
        func.setBody(body);
        
        // Handle modern function features directly in AST
        if (tree.isAsync) {
            func.setAsync(true);
        }
        if (tree.isGenerator) {
            func.setGenerator(true);
        }
        
        return func;
    }
    
    @Override
    public BaseNode convert(VariableStatementTree tree) {
        return doConvert(tree.declarations, null);
    }
    
    @Override
    public BaseNode convert(VariableDeclarationListTree tree) {
        VariableDeclaration varDecl = new VariableDeclaration();
        varDecl.setSourceFile(createSourceFile());
        
        // Preserve declaration type (var, let, const) in AST
        switch (tree.declarationType.toString()) {
            case "LET":
                varDecl.setLetInitializer(true);
                break;
            case "CONST":
                varDecl.setConst(true);
                break;
            default:
                // var is the default, no need to set anything
                break;
        }
        
        for (ParseTree declaration : tree.declarations) {
            BaseNode varInit = doConvert(declaration, varDecl);
            if (varInit instanceof VariableInitializer) {
                varDecl.addVariable((VariableInitializer) varInit);
            }
        }
        
        return varDecl;
    }
    
    @Override
    public BaseNode convert(VariableDeclarationTree tree) {
        VariableInitializer init = new VariableInitializer();
        init.setSourceFile(createSourceFile());
        
        // Set variable name
        BaseNode target = doConvert(tree.lvalue, init);
        init.setTarget(target);
        
        // Set initializer if present
        if (tree.initializer != null) {
            BaseNode initializer = doConvert(tree.initializer, init);
            init.setInitializer(initializer);
        }
        
        return init;
    }
    
    @Override
    public BaseNode convert(IfStatementTree tree) {
        IfStatement ifStmt = new IfStatement();
        ifStmt.setSourceFile(createSourceFile());
        
        // Set condition
        BaseNode condition = doConvert(tree.condition, ifStmt);
        ifStmt.setCondition(condition);
        
        // Set then statement
        BaseNode thenStmt = doConvert(tree.ifClause, ifStmt);
        ifStmt.setThen(thenStmt);
        
        // Set else statement if present
        if (tree.elseClause != null) {
            BaseNode elseStmt = doConvert(tree.elseClause, ifStmt);
            ifStmt.setElse(elseStmt);
        }
        
        return ifStmt;
    }
    
    @Override
    public BaseNode convert(ReturnStatementTree tree) {
        ReturnStatement returnStmt = new ReturnStatement();
        returnStmt.setSourceFile(createSourceFile());
        
        if (tree.expression != null) {
            BaseNode expr = doConvert(tree.expression, returnStmt);
            returnStmt.setReturnValue(expr);
        }
        
        return returnStmt;
    }
    
    @Override
    public BaseNode convert(ExpressionStatementTree tree) {
        ExpressionStatement exprStmt = new ExpressionStatement();
        exprStmt.setSourceFile(createSourceFile());
        
        BaseNode expr = doConvert(tree.expression, exprStmt);
        exprStmt.setExpression(expr);
        
        return exprStmt;
    }
    
    @Override
    public BaseNode convert(CallExpressionTree tree) {
        FunctionCall call = new FunctionCall();
        call.setSourceFile(createSourceFile());
        
        // Set target function
        BaseNode target = doConvert(tree.operand, call);
        call.setTarget(target);
        
        // Add arguments (including modern syntax like spread)
        for (ParseTree arg : tree.arguments.arguments) {
            BaseNode argNode = doConvert(arg, call);
            call.addArgument(argNode);
        }
        
        return call;
    }
    
    @Override
    public BaseNode convert(ArrayLiteralExpressionTree tree) {
        ArrayLiteral array = new ArrayLiteral();
        array.setSourceFile(createSourceFile());
        
        // Add elements (including modern syntax like spread)
        for (ParseTree element : tree.elements) {
            BaseNode elementNode = doConvert(element, array);
            array.addElement(elementNode);
        }
        
        return array;
    }
    
    @Override
    public BaseNode convert(ObjectLiteralExpressionTree tree) {
        ObjectLiteral obj = new ObjectLiteral();
        obj.setSourceFile(createSourceFile());
        
        // Add properties (including modern syntax like computed properties, spread)
        for (ParseTree property : tree.propertyNameAndValues) {
            BaseNode propNode = doConvert(property, obj);
            if (propNode instanceof ObjectProperty) {
                obj.addProperty((ObjectProperty) propNode);
            }
        }
        
        return obj;
    }
    
    @Override
    public BaseNode convert(PropertyNameAssignmentTree tree) {
        ObjectProperty prop = new ObjectProperty();
        prop.setSourceFile(createSourceFile());
        
        // Set property name
        BaseNode name = createNodeFromToken(tree.name);
        prop.setLeft(name);

        // Set property value
        BaseNode value = doConvert(tree.value, prop);
        prop.setRight(value);
        
        return prop;
    }
    
    @Override
    public BaseNode convert(IdentifierExpressionTree tree) {
        return createNodeFromToken(tree.identifierToken);
    }
    
    @Override
    public BaseNode convert(LiteralExpressionTree tree) {
        // Use the same approach as BasicClosureConverter - delegate to a helper method
        return createNodeFromToken(tree.literalToken);
    }

    /**
     * Helper method to create AST nodes from Google Closure tokens
     */
    private BaseNode createNodeFromToken(com.google.javascript.jscomp.parsing.parser.Token token) {
        if (token == null) {
            return null;
        }

        String tokenType = token.type.toString();
        String value = null;

        // Extract value based on token type
        switch (token.type) {
            case IDENTIFIER:
                value = token.asIdentifier().value;
                break;
            case STRING:
            case NUMBER:
            case TRUE:
            case FALSE:
            case NULL:
                value = token.asLiteral().value;
                break;
            default:
                value = token.toString();
                break;
        }

        switch (tokenType) {
            case "STRING":
                StringLiteral strLit = new StringLiteral();
                strLit.setValue(value);
                strLit.setSourceFile(createSourceFile());
                return strLit;

            case "NUMBER":
                NumberLiteral numLit = new NumberLiteral();
                try {
                    numLit.setValue(Double.parseDouble(value));
                } catch (NumberFormatException e) {
                    numLit.setValue(0.0);
                }
                numLit.setSourceFile(createSourceFile());
                return numLit;

            case "TRUE":
            case "FALSE":
            case "NULL":
                KeywordLiteral keywordLit = new KeywordLiteral();
                // Set the proper token code instead of using setValue
                if ("true".equals(value)) {
                    keywordLit.setCode(org.mozilla.javascript.Token.TRUE);
                } else if ("false".equals(value)) {
                    keywordLit.setCode(org.mozilla.javascript.Token.FALSE);
                } else if ("null".equals(value)) {
                    keywordLit.setCode(org.mozilla.javascript.Token.NULL);
                }
                keywordLit.setSourceFile(createSourceFile());
                return keywordLit;

            case "IDENTIFIER":
                Name name = new Name(value);
                name.setSourceFile(createSourceFile());
                return name;

            default:
                // Fallback for other token types
                StringLiteral fallback = new StringLiteral();
                fallback.setValue(value);
                fallback.setSourceFile(createSourceFile());
                return fallback;
        }
    }
    
    // ========== MODERN SYNTAX SUPPORT (NO TRANSPILATION) ==========
    
    @Override
    public BaseNode convert(ClassDeclarationTree tree) {
        ClassDeclaration classDecl = new ClassDeclaration();
        classDecl.setSourceFile(createSourceFile());
        
        // Set class name
        if (tree.name != null) {
            Name name = new Name(tree.name.value);
            classDecl.setName(name);
        }
        
        // Set superclass if present
        if (tree.superClass != null) {
            BaseNode superClass = doConvert(tree.superClass, classDecl);
            classDecl.setSuperClass(superClass);
        }
        
        // Add class elements
        for (ParseTree element : tree.elements) {
            BaseNode elementNode = doConvert(element, classDecl);
            classDecl.addElement(elementNode);
        }
        
        return classDecl;
    }
    
    @Override
    public BaseNode convert(AwaitExpressionTree tree) {
        AwaitExpression await = new AwaitExpression();
        await.setSourceFile(createSourceFile());
        
        BaseNode expression = doConvert(tree.expression, await);
        await.setExpr(expression);
        
        return await;
    }
    
    @Override
    public BaseNode convert(IterSpreadTree tree) {
        SpreadExpression spread = new SpreadExpression();
        spread.setSourceFile(createSourceFile());
        
        BaseNode expression = doConvert(tree.expression, spread);
        spread.setOperand(expression);
        
        return spread;
    }

    @Override
    public BaseNode convert(YieldExpressionTree tree) {
        YieldStatement yield = new YieldStatement();
        yield.setSourceFile(createSourceFile());

        if (tree.expression != null) {
            BaseNode expression = doConvert(tree.expression, yield);
            yield.setValue(expression);
        }

        return yield;
    }

    @Override
    public BaseNode convert(WithStatementTree tree) {
        WithStatement withStmt = new WithStatement();
        withStmt.setSourceFile(createSourceFile());

        BaseNode expr = doConvert(tree.expression, withStmt);
        withStmt.setExpr(expr);

        BaseNode statement = doConvert(tree.body, withStmt);
        withStmt.setStatement(statement);

        return withStmt;
    }

    @Override
    public BaseNode convert(UpdateExpressionTree tree) {
        Unary unary = new Unary();
        unary.setSourceFile(createSourceFile());

        BaseNode operand = doConvert(tree.operand, unary);
        unary.setOperand(operand);

        // Set operator based on the update type
        switch (tree.operator.toString()) {
            case "++":
                unary.setOperator(Operators.INC);
                break;
            case "--":
                unary.setOperator(Operators.DEC);
                break;
            default:
                unary.setOperator(Operators.getByValue(tree.operator.toString()));
                break;
        }

        unary.setPostfix(tree.operatorPosition == UpdateExpressionTree.OperatorPosition.POSTFIX);

        return unary;
    }

    // ========== STUB IMPLEMENTATIONS FOR REMAINING METHODS ==========
    // These can be implemented as needed

    @Override public BaseNode convert(ArgumentListTree tree) { return null; }
    @Override public BaseNode convert(ArrayPatternTree tree) { return null; }
    @Override public BaseNode convert(BreakStatementTree tree) { return null; }
    @Override public BaseNode convert(CaseClauseTree tree) { return null; }
    @Override public BaseNode convert(CommaExpressionTree tree) { return null; }
    @Override public BaseNode convert(com.google.javascript.jscomp.parsing.parser.trees.Comment tree) { return null; }
    @Override public BaseNode convert(ComprehensionForTree tree) { return null; }
    @Override public BaseNode convert(ComprehensionIfTree tree) { return null; }
    @Override public BaseNode convert(ComprehensionTree tree) { return null; }
    @Override public BaseNode convert(ComputedPropertyDefinitionTree tree) { return null; }
    @Override public BaseNode convert(ComputedPropertyGetterTree tree) { return null; }
    @Override public BaseNode convert(ComputedPropertyMethodTree tree) { return null; }
    @Override public BaseNode convert(ComputedPropertySetterTree tree) { return null; }
    @Override public BaseNode convert(ConditionalExpressionTree tree) { return null; }
    @Override public BaseNode convert(ContinueStatementTree tree) { return null; }
    @Override public BaseNode convert(DebuggerStatementTree tree) { return null; }
    @Override public BaseNode convert(DefaultClauseTree tree) { return null; }
    @Override public BaseNode convert(DefaultParameterTree tree) { return null; }
    @Override public BaseNode convert(DoWhileStatementTree tree) { return null; }
    @Override public BaseNode convert(DynamicImportTree tree) { return null; }
    @Override public BaseNode convert(EmptyStatementTree tree) { return null; }
    @Override public BaseNode convert(ExportDeclarationTree tree) { return null; }
    @Override public BaseNode convert(ExportSpecifierTree tree) { return null; }
    @Override public BaseNode convert(FieldDeclarationTree tree) { return null; }
    @Override public BaseNode convert(FinallyTree tree) { return null; }
    @Override public BaseNode convert(ForAwaitOfStatementTree tree) { return null; }
    @Override public BaseNode convert(ForInStatementTree tree) { return null; }
    @Override public BaseNode convert(FormalParameterListTree tree) { return null; }
    @Override public BaseNode convert(ForOfStatementTree tree) { return null; }
    @Override public BaseNode convert(GetAccessorTree tree) { return null; }
    @Override public BaseNode convert(ImportDeclarationTree tree) { return null; }
    @Override public BaseNode convert(ImportMetaExpressionTree tree) { return null; }
    @Override public BaseNode convert(ImportSpecifierTree tree) { return null; }
    @Override public BaseNode convert(IterRestTree tree) { return null; }
    @Override public BaseNode convert(LabelledStatementTree tree) { return null; }
    @Override public BaseNode convert(MemberExpressionTree tree) { return null; }
    @Override public BaseNode convert(MemberLookupExpressionTree tree) { return null; }
    @Override public BaseNode convert(MissingPrimaryExpressionTree tree) { return null; }
    @Override public BaseNode convert(NewExpressionTree tree) { return null; }
    @Override public BaseNode convert(NewTargetExpressionTree tree) { return null; }
    @Override public BaseNode convert(NullTree tree) { return null; }
    @Override public BaseNode convert(ObjectPatternTree tree) { return null; }
    @Override public BaseNode convert(ObjectRestTree tree) { return null; }
    @Override public BaseNode convert(ObjectSpreadTree tree) { return null; }
    // @Override public BaseNode convert(OptChainCallExpressionTree tree) { return null; }
    @Override public BaseNode convert(OptionalMemberLookupExpressionTree tree) { return null; }
    @Override public BaseNode convert(ParenExpressionTree tree) { return null; }
    @Override public BaseNode convert(ParseTree tree) { return null; }
    @Override public BaseNode convert(SetAccessorTree tree) { return null; }
    @Override public BaseNode convert(SuperExpressionTree tree) { return null; }
    @Override public BaseNode convert(SwitchStatementTree tree) { return null; }
    @Override public BaseNode convert(TemplateLiteralPortionTree tree) { return null; }
    @Override public BaseNode convert(TemplateSubstitutionTree tree) { return null; }
    @Override public BaseNode convert(ThisExpressionTree tree) { return null; }
    @Override public BaseNode convert(ThrowStatementTree tree) { return null; }
    @Override public BaseNode convert(UnaryExpressionTree tree) { return null; }

    @Override
    public BaseNode convert(TemplateLiteralExpressionTree tree) {
        TemplateLiteralExpression template = new TemplateLiteralExpression();
        template.setSourceFile(createSourceFile());

        for (ParseTree element : tree.elements) {
            BaseNode elementNode = doConvert(element, template);
            template.addElement(elementNode);
        }

        return template;
    }

    @Override
    public BaseNode convert(OptionalMemberExpressionTree tree) {
        OptionalMemberExpression optMember = new OptionalMemberExpression();
        optMember.setSourceFile(createSourceFile());

        BaseNode operand = doConvert(tree.operand, optMember);
        optMember.setLeft(operand);

        if (tree.memberName != null) {
            BaseNode memberName = createNodeFromToken(tree.memberName);
            optMember.setRight(memberName);
        }

        return optMember;
    }

    @Override
    public BaseNode convert(BinaryOperatorTree tree) {
        String operator = tree.operator.type.toString();

        // Handle assignment operators
        if (isAssignmentOperator(operator)) {
            Assignment assignment = new Assignment();
            assignment.setSourceFile(createSourceFile());

            BaseNode left = doConvert(tree.left, assignment);
            BaseNode right = doConvert(tree.right, assignment);

            assignment.setLeft(left);
            assignment.setRight(right);
            assignment.setOperator(convertOperator(operator));

            return assignment;
        } else {
            // Handle binary expressions
            Infix infix = new Infix();
            infix.setSourceFile(createSourceFile());

            BaseNode left = doConvert(tree.left, infix);
            BaseNode right = doConvert(tree.right, infix);

            infix.setLeft(left);
            infix.setRight(right);
            infix.setOperator(convertOperator(operator));

            return infix;
        }
    }

    @Override
    public BaseNode convert(ForStatementTree tree) {
        ForLoop forLoop = new ForLoop();
        forLoop.setSourceFile(createSourceFile());

        // Set initializer
        if (tree.initializer != null) {
            BaseNode init = doConvert(tree.initializer, forLoop);
            forLoop.setInitializer(init);
        }

        // Set condition
        if (tree.condition != null) {
            BaseNode condition = doConvert(tree.condition, forLoop);
            forLoop.setCondition(condition);
        }

        // Set increment
        if (tree.increment != null) {
            BaseNode increment = doConvert(tree.increment, forLoop);
            forLoop.setIncrement(increment);
        }

        // Set body
        BaseNode body = doConvert(tree.body, forLoop);
        forLoop.setBody(body);

        return forLoop;
    }

    @Override
    public BaseNode convert(WhileStatementTree tree) {
        WhileLoop whileLoop = new WhileLoop();
        whileLoop.setSourceFile(createSourceFile());

        BaseNode condition = doConvert(tree.condition, whileLoop);
        whileLoop.setCondition(condition);

        BaseNode body = doConvert(tree.body, whileLoop);
        whileLoop.setBody(body);

        return whileLoop;
    }

    @Override
    public BaseNode convert(TryStatementTree tree) {
        TryStatement tryStmt = new TryStatement();
        tryStmt.setSourceFile(createSourceFile());

        // Set try block
        BaseNode tryBlock = doConvert(tree.body, tryStmt);
        tryStmt.setTryBlock(tryBlock);

        // Set catch clause
        if (tree.catchBlock != null) {
            BaseNode catchBlock = doConvert(tree.catchBlock, tryStmt);
            if (catchBlock instanceof CatchClause) {
                tryStmt.addCatchClause(catchBlock);
            }
        }

        // Set finally block
        if (tree.finallyBlock != null) {
            BaseNode finallyBlock = doConvert(tree.finallyBlock, tryStmt);
            tryStmt.setFinallyBlock(finallyBlock);
        }

        return tryStmt;
    }

    @Override
    public BaseNode convert(CatchTree tree) {
        CatchClause catchClause = new CatchClause();
        catchClause.setSourceFile(createSourceFile());

        // Set exception parameter
        if (tree.exception != null) {
            BaseNode param = doConvert(tree.exception, catchClause);
            if (param instanceof Name) {
                catchClause.setName((Name) param);
            }
        }

        // Set catch body
        BaseNode body = doConvert(tree.catchBody, catchClause);
        if (body instanceof Block) {
            catchClause.setBody((Block) body);
        }

        return catchClause;
    }

    // ========== UTILITY METHODS ==========

    private boolean isAssignmentOperator(String operator) {
        return operator.equals("=") || operator.equals("+=") || operator.equals("-=") ||
               operator.equals("*=") || operator.equals("/=") || operator.equals("%=");
    }

    private Operators convertOperator(String operator) {
        switch (operator) {
            case "=": return Operators.ASSIGN;
            case "+": return Operators.ADD;
            case "-": return Operators.SUB;
            case "*": return Operators.MUL;
            case "/": return Operators.DIV;
            case "%": return Operators.MOD;
            case "==": return Operators.EQ;
            case "!=": return Operators.NE;
            case "<": return Operators.LT;
            case ">": return Operators.GT;
            case "<=": return Operators.LE;
            case ">=": return Operators.GE;
            case "&&": return Operators.AND;
            case "||": return Operators.OR;
            case "??": return Operators.getByValue("??"); // Modern syntax preserved
            default: return Operators.getByValue(operator);
        }
    }

    private com.sencha.tools.compiler.sources.SourceFile createSourceFile() {
        // For now, return null since SourceFile requires complex dependencies
        // The AST nodes will still work without SourceFile attached
        return null;
    }
}

/*
 * Copyright (c) 2012-2023. Sencha Inc.
 */

package com.sencha.tools.compiler.ast;

import com.google.javascript.jscomp.parsing.parser.trees.ProgramTree;
import com.sencha.tools.compiler.ast.js.BaseNode;
import com.sencha.tools.compiler.ast.js.PassthroughNode;
import com.sencha.tools.compiler.ast.js.RootNode;
import com.sencha.tools.compiler.ast.js.SimplePassthroughNode;
import com.sencha.tools.compressors.JsLanguageLevel;

/**
 * Example demonstrating how passthrough nodes handle unidentified expressions
 * in the AST conversion process.
 */
public class PassthroughExample {

    public static void main(String[] args) {
        // Example JavaScript code with various modern syntax features
        String jsCode = """
            // Modern JavaScript features that might not be fully supported
            const obj = {
                // Optional chaining
                value: data?.user?.name,
                
                // Nullish coalescing
                fallback: value ?? 'default',
                
                // Private class fields (if supported by parser)
                #privateField: 'secret',
                
                // Dynamic imports
                async loadModule() {
                    const module = await import('./module.js');
                    return module.default;
                },
                
                // BigInt literals
                bigNumber: 123456789012345678901234567890n
            };
            
            // Class with modern features
            class MyClass {
                #private = 'hidden';
                
                static {
                    // Static initialization block
                    console.log('Class initialized');
                }
                
                async *generator() {
                    yield* await this.getData();
                }
            }
            """;

        System.out.println("=== Passthrough Node Example ===");
        System.out.println("JavaScript Code:");
        System.out.println(jsCode);
        System.out.println();

        try {
            // Test with BasicClosureConverter (enhanced with fallback passthrough)
            System.out.println("=== BasicClosureConverter with Fallback Passthrough ===");
            BasicClosureConverter basicConverter = new BasicClosureConverter();
            basicConverter.setOriginalSource(jsCode);
            basicConverter.setSourceName("example.js");
            
            ProgramTree program = ClosureParserUtil.parseOnly(jsCode, "example.js", JsLanguageLevel.NEXT);
            RootNode root = (RootNode) basicConverter.convert(program);
            
            analyzeAST(root, "BasicClosureConverter");
            System.out.println();

            // Test with SimpleClosureConverter (selective passthrough)
            System.out.println("=== SimpleClosureConverter with Selective Passthrough ===");
            SimpleClosureConverter simpleConverter = new SimpleClosureConverter();
            simpleConverter.setOriginalSource(jsCode);
            simpleConverter.setSourceName("example.js");
            
            ProgramTree simpleProgram = ClosureParserUtil.parseOnly(jsCode, "example.js", JsLanguageLevel.NEXT);
            RootNode simpleRoot = (RootNode) simpleConverter.convert(simpleProgram);
            
            analyzeAST(simpleRoot, "SimpleClosureConverter");

        } catch (Exception e) {
            System.err.println("Error during conversion: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Analyzes the AST and reports on passthrough nodes found.
     */
    private static void analyzeAST(BaseNode root, String converterName) {
        PassthroughAnalyzer analyzer = new PassthroughAnalyzer();
        analyzer.visit(root);
        
        System.out.println(converterName + " Results:");
        System.out.println("  Total nodes: " + analyzer.getTotalNodes());
        System.out.println("  PassthroughNode instances: " + analyzer.getPassthroughCount());
        System.out.println("  SimplePassthroughNode instances: " + analyzer.getSimplePassthroughCount());
        System.out.println("  Passthrough ratio: " + 
                          String.format("%.1f%%", analyzer.getPassthroughRatio() * 100));
        
        if (analyzer.getPassthroughCount() > 0) {
            System.out.println("  PassthroughNode types found:");
            for (String type : analyzer.getPassthroughTypes()) {
                System.out.println("    - " + type);
            }
        }
        
        if (analyzer.getSimplePassthroughCount() > 0) {
            System.out.println("  SimplePassthroughNode types found:");
            for (String type : analyzer.getSimplePassthroughTypes()) {
                System.out.println("    - " + type);
            }
        }
    }

    /**
     * Visitor class to analyze passthrough nodes in the AST.
     */
    private static class PassthroughAnalyzer extends com.sencha.tools.compiler.ast.js.BaseNodeVisitor<Void> {
        private int totalNodes = 0;
        private int passthroughCount = 0;
        private int simplePassthroughCount = 0;
        private java.util.Set<String> passthroughTypes = new java.util.HashSet<>();
        private java.util.Set<String> simplePassthroughTypes = new java.util.HashSet<>();

        @Override
        public void visit(BaseNode node) {
            if (node != null) {
                totalNodes++;
                super.visit(node);
            }
        }

        @Override
        public void onPassthroughNode(PassthroughNode node) {
            passthroughCount++;
            passthroughTypes.add(node.getTreeType());
        }

        @Override
        public void onSimplePassthroughNode(SimplePassthroughNode node) {
            simplePassthroughCount++;
            simplePassthroughTypes.add(node.getNodeType());
        }

        public int getTotalNodes() { return totalNodes; }
        public int getPassthroughCount() { return passthroughCount; }
        public int getSimplePassthroughCount() { return simplePassthroughCount; }
        public java.util.Set<String> getPassthroughTypes() { return passthroughTypes; }
        public java.util.Set<String> getSimplePassthroughTypes() { return simplePassthroughTypes; }
        
        public double getPassthroughRatio() {
            return totalNodes > 0 ? (double)(passthroughCount + simplePassthroughCount) / totalNodes : 0.0;
        }
    }
}
